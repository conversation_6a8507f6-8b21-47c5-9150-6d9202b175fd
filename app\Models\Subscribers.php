<?php

namespace App\Models;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Subscribers extends Model
{
    use HasFactory;

    protected $fillable = [
        "user_id",
        "phone_num",
        "subscription",
        "country",
        "address",
        "registration_number",
        "tin_number",
        "company_name",
        "company_website_link",
        "company_description",
        "licence_file",
        "company_logo",
        "status",
        'aproval',
        'rejected',
    ];
    // Define any relationships here
    public function user()
    {
        return $this->belongsTo(User::class);
    }

}
