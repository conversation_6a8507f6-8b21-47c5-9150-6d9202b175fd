<?php

use App\Http\Controllers\dashboardController;
use App\Http\Controllers\destinationCrudController;
use App\Http\Controllers\reportsController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
 */

Route::prefix('dashboard')->group(function () {

    // Route::get('/home', function () {
    //     return view('dashboard.index');
    // });
    Route::get('/home', [dashboardController::class, 'index'])->middleware(['verified', 'auth']);

    Route::get('/website/seo/settings', [dashboardController::class, 'websiteSEOSettings'])->middleware(['verified', 'auth', 'admin']);


    Route::get('/commission/setting', [dashboardController::class, 'commissionSetting'])->middleware(['verified', 'auth', 'admin']);
  
   Route::get('/reviews/list', [dashboardController::class, 'reviewsList'])->middleware(['verified', 'auth', 'admin']);



    Route::get('/packages', function () {
        return view('dashboard.packages.packageList');
    })->middleware(['verified', 'auth', 'admin']);

    Route::get('/agents', function () {
        return view('dashboard.agents.agentList');
    })->middleware(['verified', 'auth', 'admin']);

    // Route::get('/add-destination', function () {
    //     return view('dashboard.destinations.addDestination');
    // });

    Route::get('/profile', function () {
        return view('dashboard.adminProfile.profile');
    })->middleware(['verified', 'auth', 'admin']);

    Route::get('/website/settings', [dashboardController::class, 'websiteSettings'])->middleware(['verified', 'auth', 'admin']);

    //custom trip requests routes
    Route::get('/custom/trip/request/list', [dashboardController::class, 'customTripList'])->middleware(['verified', 'auth', 'admin']);
    Route::get('/custom/trip/request/list/update/{id}', [dashboardController::class, 'customTripListUpdateView'])->name('dashboard.customTripRequest.updateCustomTrip')->middleware(['verified', 'auth', 'admin']);

    //custom trip detail route
    Route::get('/custom/trip/details/{id}', [dashboardController::class, 'customTripDetail'])->middleware(['verified', 'auth', 'admin']);

    //custom trip payment listing routes
    Route::get('/custom/trip/payment/list', [dashboardController::class, 'customTripPaymentList'])->middleware(['verified', 'auth', 'admin']);


    //trip booking payment listing routes
    Route::get('/booking/trip/payment/list', [dashboardController::class, 'bookingTripPaymentList'])->middleware(['verified', 'auth', 'agent']);

    // destination booking
    Route::get('/destination/booking/list', [dashboardController::class, 'destinationBooking'])->middleware(['verified', 'auth', 'agent']);
    Route::get('/destination/booking/list/{id}', [dashboardController::class, 'destinationBooking'])->middleware(['verified', 'auth', 'agent']);
    Route::get('/destination/booking/list/update/{id}', [destinationCrudController::class, 'showDestinationBooking'])->name('dashboard.destinationBooking.updateBooking')->middleware(['verified', 'auth', 'agent']);
   
    //reportings
    Route::get('/reports', [reportsController::class, 'BookingReport'])->middleware(['verified', 'auth', 'agent']);

});
Route::post('/api/destination/booking/detail/update/{id}', [destinationCrudController::class, 'updateDestinationBooking'])->middleware(['verified', 'auth', 'agent']);

  Route::post('/api/commission/add', [dashboardController::class, 'commissionAdd'])->middleware(['verified', 'auth', 'admin']);
 Route::delete('/api/commission/delete/{id}', [dashboardController::class, 'commissionDelete'])->middleware(['verified', 'auth', 'admin']);
 Route::post('/api/commission/update/{id}', [dashboardController::class, 'commissionUpdate'])->middleware(['verified', 'auth', 'admin']);

 Route::delete('/api/review/delete/{id}', [dashboardController::class, 'reviewDelete'])->middleware(['verified', 'auth', 'admin']);

 Route::put('/api/review/status/{id}', [dashboardController::class, 'reviewStatusUpdate'])->middleware(['verified', 'auth', 'admin']);


/*agenst*/
// Route::get('/agents-list', function () {
//     return view('dashboard.agents-list');
// });
// Route::get('/package-list', function () {
//     return view('dashboard.package-list');
// });
// Route::get('/add-new-package', function () {
//     return view('dashboard.add-new-package');
// });
